import android.content.Context;
import android.content.Intent;
import java.util.ArrayList;
import java.util.List;

/**
 * 文档管理器 - 用于管理多文档预览
 */
public class DocumentManager {
    
    /**
     * 文档信息类
     */
    public static class Document {
        private String filePath;
        private String tempPath;
        private String fileName;
        private String fileType;
        
        public Document(String filePath, String tempPath, String fileName) {
            this.filePath = filePath;
            this.tempPath = tempPath;
            this.fileName = fileName;
            this.fileType = getFileExtension(filePath);
        }
        
        public Document(String filePath, String tempPath, String fileName, String fileType) {
            this.filePath = filePath;
            this.tempPath = tempPath;
            this.fileName = fileName;
            this.fileType = fileType;
        }
        
        private String getFileExtension(String filePath) {
            if (filePath != null && filePath.contains(".")) {
                return filePath.substring(filePath.lastIndexOf(".") + 1).toLowerCase();
            }
            return "";
        }
        
        // Getters
        public String getFilePath() { return filePath; }
        public String getTempPath() { return tempPath; }
        public String getFileName() { return fileName; }
        public String getFileType() { return fileType; }
        
        // Setters
        public void setFilePath(String filePath) { this.filePath = filePath; }
        public void setTempPath(String tempPath) { this.tempPath = tempPath; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        public void setFileType(String fileType) { this.fileType = fileType; }
        
        /**
         * 检查文件类型是否支持预览
         */
        public boolean isSupportedFormat() {
            String[] supportedFormats = {
                "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
                "txt", "rtf", "epub", "mobi", "fb2", "chm"
            };
            
            for (String format : supportedFormats) {
                if (format.equals(fileType)) {
                    return true;
                }
            }
            return false;
        }
    }
    
    private List<Document> documents;
    private Context context;
    
    public DocumentManager(Context context) {
        this.context = context;
        this.documents = new ArrayList<>();
    }
    
    /**
     * 添加单个文档
     */
    public void addDocument(String filePath, String tempPath, String fileName) {
        Document doc = new Document(filePath, tempPath, fileName);
        if (doc.isSupportedFormat()) {
            documents.add(doc);
        }
    }
    
    /**
     * 添加文档对象
     */
    public void addDocument(Document document) {
        if (document != null && document.isSupportedFormat()) {
            documents.add(document);
        }
    }
    
    /**
     * 批量添加文档
     */
    public void addDocuments(List<Document> documentList) {
        if (documentList != null) {
            for (Document doc : documentList) {
                addDocument(doc);
            }
        }
    }
    
    /**
     * 移除文档
     */
    public void removeDocument(int index) {
        if (index >= 0 && index < documents.size()) {
            documents.remove(index);
        }
    }
    
    /**
     * 移除文档
     */
    public void removeDocument(Document document) {
        documents.remove(document);
    }
    
    /**
     * 清空所有文档
     */
    public void clearDocuments() {
        documents.clear();
    }
    
    /**
     * 获取文档列表
     */
    public List<Document> getDocuments() {
        return new ArrayList<>(documents);
    }
    
    /**
     * 获取文档数量
     */
    public int getDocumentCount() {
        return documents.size();
    }
    
    /**
     * 检查是否有文档
     */
    public boolean hasDocuments() {
        return !documents.isEmpty();
    }
    
    /**
     * 启动多文档预览
     */
    public void startMultiDocumentPreview() {
        if (documents.isEmpty()) {
            return;
        }
        
        Intent intent = new Intent(context, DocumentPreviewActivity.class);
        
        if (documents.size() == 1) {
            // 单文档模式
            Document doc = documents.get(0);
            intent.putExtra("filePath", doc.getFilePath());
            intent.putExtra("tempPath", doc.getTempPath());
            intent.putExtra("fileName", doc.getFileName());
        } else {
            // 多文档模式
            ArrayList<String> filePaths = new ArrayList<>();
            ArrayList<String> tempPaths = new ArrayList<>();
            ArrayList<String> fileNames = new ArrayList<>();
            
            for (Document doc : documents) {
                filePaths.add(doc.getFilePath());
                tempPaths.add(doc.getTempPath());
                fileNames.add(doc.getFileName());
            }
            
            intent.putStringArrayListExtra("documentPaths", filePaths);
            intent.putStringArrayListExtra("documentTempPaths", tempPaths);
            intent.putStringArrayListExtra("documentNames", fileNames);
        }
        
        context.startActivity(intent);
    }
    
    /**
     * 启动单文档预览
     */
    public static void startSingleDocumentPreview(Context context, String filePath, String tempPath, String fileName) {
        Intent intent = new Intent(context, DocumentPreviewActivity.class);
        intent.putExtra("filePath", filePath);
        intent.putExtra("tempPath", tempPath);
        intent.putExtra("fileName", fileName);
        context.startActivity(intent);
    }
    
    /**
     * 获取支持的文件格式列表
     */
    public static String[] getSupportedFormats() {
        return new String[]{
            "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
            "txt", "rtf", "epub", "mobi", "fb2", "chm"
        };
    }
    
    /**
     * 检查文件是否支持预览
     */
    public static boolean isFileSupported(String filePath) {
        if (filePath == null || !filePath.contains(".")) {
            return false;
        }
        
        String extension = filePath.substring(filePath.lastIndexOf(".") + 1).toLowerCase();
        String[] supportedFormats = getSupportedFormats();
        
        for (String format : supportedFormats) {
            if (format.equals(extension)) {
                return true;
            }
        }
        return false;
    }
}
