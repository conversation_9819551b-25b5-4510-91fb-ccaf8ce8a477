@echo off
echo ========================================
echo Java 11 Installation Script
echo ========================================

echo.
echo 1. Checking for existing Java 11 installations...

if exist "C:\Program Files\Eclipse Adoptium\jdk-11*" (
    echo [SUCCESS] Found Java 11 in Eclipse Adoptium directory
    for /d %%i in ("C:\Program Files\Eclipse Adoptium\jdk-11*") do set JAVA11_PATH=%%i
    goto :set_env
)

if exist "C:\Program Files\Java\jdk-11*" (
    echo [SUCCESS] Found Java 11 in Java directory
    for /d %%i in ("C:\Program Files\Java\jdk-11*") do set JAVA11_PATH=%%i
    goto :set_env
)

if exist "C:\Program Files\OpenJDK\jdk-11*" (
    echo [SUCCESS] Found Java 11 in OpenJDK directory
    for /d %%i in ("C:\Program Files\OpenJDK\jdk-11*") do set JAVA11_PATH=%%i
    goto :set_env
)

echo [INFO] Java 11 not found. Checking for Chocolatey...

where choco >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [INFO] Chocolatey not found. Please install Java 11 manually from:
    echo https://adoptium.net/
    echo.
    echo After installation, set JAVA_HOME environment variable to the JDK path
    pause
    exit /b 1
) else (
    echo [SUCCESS] Chocolatey found. Installing Java 11...
    choco install openjdk11 -y
    if %ERRORLEVEL% NEQ 0 (
        echo [ERROR] Failed to install Java 11 via Chocolatey
        pause
        exit /b 1
    )
    
    echo [SUCCESS] Java 11 installed. Searching for installation path...
    
    if exist "C:\Program Files\Eclipse Adoptium\jdk-11*" (
        for /d %%i in ("C:\Program Files\Eclipse Adoptium\jdk-11*") do set JAVA11_PATH=%%i
        goto :set_env
    )
    
    echo [ERROR] Could not find Java 11 installation path after installation
    pause
    exit /b 1
)

:set_env
echo.
echo 2. Setting environment variables...
echo [SUCCESS] Java 11 found at: %JAVA11_PATH%

set JAVA_HOME=%JAVA11_PATH%
set PATH=%JAVA11_PATH%\bin;%PATH%

echo [SUCCESS] JAVA_HOME set to: %JAVA_HOME%

echo.
echo 3. Verifying Java installation...
"%JAVA11_PATH%\bin\java.exe" -version
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Java verification failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Java 11 setup completed successfully!
echo ========================================

echo.
echo Next steps:
echo 1. Restart your terminal or VSCode
echo 2. Run: gradlew.bat clean
echo 3. Run: gradlew.bat assembleDebug

echo.
echo To permanently set JAVA_HOME, add this to your system environment variables:
echo JAVA_HOME=%JAVA11_PATH%

echo.
echo Press any key to continue...
pause >nul
