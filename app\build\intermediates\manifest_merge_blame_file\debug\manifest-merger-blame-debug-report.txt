1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.documentpreview"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="30" />
9-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- 存储权限 -->
16    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
16-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:10:5-80
16-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:10:22-77
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:11:5-81
17-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:11:22-78
18
19    <!-- 文件访问权限 -->
20    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
20-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:14:5-82
20-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:14:22-79
21
22    <application
22-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:16:5-94:19
23        android:allowBackup="true"
23-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:17:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\3732dbaed7efee9bf6fb58da2e28e003\core-1.6.0\AndroidManifest.xml:24:18-86
25        android:debuggable="true"
26        android:icon="@drawable/ic_launcher"
26-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:18:9-45
27        android:label="@string/app_name"
27-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:19:9-41
28        android:requestLegacyExternalStorage="true"
28-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:21:9-52
29        android:theme="@style/AppTheme" >
29-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:20:9-40
30
31        <!-- 主Activity -->
32        <activity
32-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:24:9-82:20
33            android:name="com.example.documentpreview.MainActivity"
33-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:25:13-41
34            android:exported="true"
34-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:26:13-36
35            android:label="@string/app_name" >
35-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:27:13-45
36            <intent-filter>
36-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:28:13-31:29
37                <action android:name="android.intent.action.MAIN" />
37-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:29:17-69
37-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:29:25-66
38
39                <category android:name="android.intent.category.LAUNCHER" />
39-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:30:17-77
39-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:30:27-74
40            </intent-filter>
41
42            <!-- 支持打开文档文件 -->
43            <intent-filter>
43-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:34:13-39:29
44                <action android:name="android.intent.action.VIEW" />
44-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:35:17-69
44-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:35:25-66
45
46                <category android:name="android.intent.category.DEFAULT" />
46-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:36:17-76
46-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:36:27-73
47                <category android:name="android.intent.category.BROWSABLE" />
47-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:37:17-78
47-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:37:27-75
48
49                <data android:mimeType="application/pdf" />
49-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:38:17-60
49-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:38:23-57
50            </intent-filter>
51            <intent-filter>
51-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:41:13-46:29
52                <action android:name="android.intent.action.VIEW" />
52-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:35:17-69
52-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:35:25-66
53
54                <category android:name="android.intent.category.DEFAULT" />
54-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:36:17-76
54-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:36:27-73
55                <category android:name="android.intent.category.BROWSABLE" />
55-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:37:17-78
55-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:37:27-75
56
57                <data android:mimeType="application/msword" />
57-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:38:17-60
57-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:38:23-57
58            </intent-filter>
59            <intent-filter>
59-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:48:13-53:29
60                <action android:name="android.intent.action.VIEW" />
60-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:35:17-69
60-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:35:25-66
61
62                <category android:name="android.intent.category.DEFAULT" />
62-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:36:17-76
62-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:36:27-73
63                <category android:name="android.intent.category.BROWSABLE" />
63-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:37:17-78
63-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:37:27-75
64
65                <data android:mimeType="application/vnd.openxmlformats-officedocument.wordprocessingml.document" />
65-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:38:17-60
65-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:38:23-57
66            </intent-filter>
67            <intent-filter>
67-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:55:13-60:29
68                <action android:name="android.intent.action.VIEW" />
68-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:35:17-69
68-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:35:25-66
69
70                <category android:name="android.intent.category.DEFAULT" />
70-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:36:17-76
70-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:36:27-73
71                <category android:name="android.intent.category.BROWSABLE" />
71-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:37:17-78
71-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:37:27-75
72
73                <data android:mimeType="application/vnd.ms-excel" />
73-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:38:17-60
73-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:38:23-57
74            </intent-filter>
75            <intent-filter>
75-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:62:13-67:29
76                <action android:name="android.intent.action.VIEW" />
76-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:35:17-69
76-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:35:25-66
77
78                <category android:name="android.intent.category.DEFAULT" />
78-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:36:17-76
78-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:36:27-73
79                <category android:name="android.intent.category.BROWSABLE" />
79-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:37:17-78
79-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:37:27-75
80
81                <data android:mimeType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" />
81-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:38:17-60
81-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:38:23-57
82            </intent-filter>
83            <intent-filter>
83-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:69:13-74:29
84                <action android:name="android.intent.action.VIEW" />
84-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:35:17-69
84-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:35:25-66
85
86                <category android:name="android.intent.category.DEFAULT" />
86-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:36:17-76
86-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:36:27-73
87                <category android:name="android.intent.category.BROWSABLE" />
87-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:37:17-78
87-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:37:27-75
88
89                <data android:mimeType="application/vnd.ms-powerpoint" />
89-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:38:17-60
89-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:38:23-57
90            </intent-filter>
91            <intent-filter>
91-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:76:13-81:29
92                <action android:name="android.intent.action.VIEW" />
92-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:35:17-69
92-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:35:25-66
93
94                <category android:name="android.intent.category.DEFAULT" />
94-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:36:17-76
94-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:36:27-73
95                <category android:name="android.intent.category.BROWSABLE" />
95-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:37:17-78
95-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:37:27-75
96
97                <data android:mimeType="application/vnd.openxmlformats-officedocument.presentationml.presentation" />
97-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:38:17-60
97-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:38:23-57
98            </intent-filter>
99        </activity>
100
101        <!-- 文档预览Activity -->
102        <activity
102-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:85:9-92:20
103            android:name="com.example.documentpreview.DocumentPreviewActivity"
103-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:86:13-52
104            android:configChanges="orientation|screenSize|keyboardHidden"
104-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:89:13-74
105            android:exported="false"
105-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:87:13-37
106            android:hardwareAccelerated="true"
106-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:90:13-47
107            android:label="文档预览"
107-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:88:13-33
108            android:theme="@style/AppTheme.NoActionBar" >
108-->C:\Users\<USER>\Documents\augment-projects\andorid-doc-preview\app\src\main\AndroidManifest.xml:91:13-56
109        </activity>
110    </application>
111
112</manifest>
