@echo off
echo ========================================
echo Installing Android SDK Components
echo ========================================

set ANDROID_HOME=D:\my-soft\android-sdk
set ANDROID_SDK_ROOT=%ANDROID_HOME%
set PATH=%ANDROID_HOME%\platform-tools;%ANDROID_HOME%\tools;%PATH%

echo Android SDK Path: %ANDROID_HOME%

echo.
echo 1. Checking for sdkmanager...

REM Try different possible locations for sdkmanager
set SDKMANAGER=""
if exist "%ANDROID_HOME%\cmdline-tools\latest\bin\sdkmanager.bat" (
    set SDKMANAGER=%ANDROID_HOME%\cmdline-tools\latest\bin\sdkmanager.bat
    echo Found sdkmanager at: %SDKMANAGER%
) else if exist "%ANDROID_HOME%\tools\bin\sdkmanager.bat" (
    set SDKMANAGER=%ANDROID_HOME%\tools\bin\sdkmanager.bat
    echo Found sdkmanager at: %SDKMANAGER%
) else (
    echo [ERROR] sdkmanager not found!
    echo Please install Android SDK Command Line Tools
    echo.
    echo You can download them from:
    echo https://developer.android.com/studio#command-tools
    echo.
    echo Extract to: %ANDROID_HOME%\cmdline-tools\latest\
    pause
    exit /b 1
)

echo.
echo 2. Installing required SDK components...

echo Installing Android API 30...
%SDKMANAGER% "platforms;android-30"
if %ERRORLEVEL% NEQ 0 (
    echo [WARNING] Failed to install Android API 30
)

echo Installing Build Tools 30.0.3...
%SDKMANAGER% "build-tools;30.0.3"
if %ERRORLEVEL% NEQ 0 (
    echo [WARNING] Failed to install Build Tools 30.0.3
)

echo Installing Platform Tools...
%SDKMANAGER% "platform-tools"
if %ERRORLEVEL% NEQ 0 (
    echo [WARNING] Failed to install Platform Tools
)

echo.
echo 3. Listing installed packages...
%SDKMANAGER% --list_installed

echo.
echo ========================================
echo SDK Component Installation Complete
echo ========================================

echo.
echo You can now try building the project:
echo   .\gradlew.bat clean assembleDebug

echo.
echo Press any key to continue...
pause >nul
