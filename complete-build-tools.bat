@echo off
echo ========================================
echo Completing Build Tools 30.0.3 Setup
echo ========================================

set SOURCE_DIR=D:\my-soft\android-sdk\build-tools\36.0.0
set TARGET_DIR=D:\my-soft\android-sdk\build-tools\30.0.3

echo Copying all missing tools from 36.0.0 to 30.0.3...

REM Copy all .exe files
copy "%SOURCE_DIR%\*.exe" "%TARGET_DIR%\" >nul 2>&1

REM Copy all .bat files
copy "%SOURCE_DIR%\*.bat" "%TARGET_DIR%\" >nul 2>&1

REM Copy all .jar files from lib directory
if exist "%SOURCE_DIR%\lib" (
    if not exist "%TARGET_DIR%\lib" mkdir "%TARGET_DIR%\lib"
    copy "%SOURCE_DIR%\lib\*.jar" "%TARGET_DIR%\lib\" >nul 2>&1
)

echo [SUCCESS] Copied all executable files

echo.
echo Verifying critical files...

set CRITICAL_FILES=aapt.exe aapt2.exe aidl.exe zipalign.exe dexdump.exe llvm-rs-cc.exe d8.bat apksigner.bat

for %%f in (%CRITICAL_FILES%) do (
    if exist "%TARGET_DIR%\%%f" (
        echo [OK] %%f
    ) else (
        echo [MISSING] %%f
    )
)

echo.
echo Checking lib directory...
if exist "%TARGET_DIR%\lib\dx.jar" (
    echo [OK] dx.jar
) else (
    echo [MISSING] dx.jar
)

if exist "%TARGET_DIR%\lib\d8.jar" (
    echo [OK] d8.jar
) else (
    echo [MISSING] d8.jar
)

echo.
echo Checking renderscript directory...
if exist "%TARGET_DIR%\renderscript\include" (
    echo [OK] renderscript directory
) else (
    echo [MISSING] renderscript directory
)

echo.
echo ========================================
echo Build Tools 30.0.3 Setup Complete
echo ========================================

echo.
echo You can now try building the project:
echo   .\gradlew.bat assembleDebug

echo.
echo Press any key to continue...
pause >nul
