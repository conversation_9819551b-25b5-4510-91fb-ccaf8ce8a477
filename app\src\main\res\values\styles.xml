<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here -->
        <item name="colorPrimary">#2196F3</item>
        <item name="colorPrimaryDark">#1976D2</item>
        <item name="colorAccent">#FF4081</item>
    </style>

    <!-- Theme for DocumentPreviewActivity without action bar -->
    <style name="AppTheme.NoActionBar" parent="AppTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
    </style>

    <!-- Document tab button style -->
    <style name="DocumentTabButton">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">4dp</item>
        <item name="android:padding">12dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:background">@drawable/tab_button_selector</item>
        <item name="android:textColor">@color/tab_text_selector</item>
        <item name="android:minWidth">80dp</item>
        <item name="android:gravity">center</item>
    </style>

    <!-- Close button style -->
    <style name="CloseButton">
        <item name="android:layout_width">32dp</item>
        <item name="android:layout_height">32dp</item>
        <item name="android:layout_margin">2dp</item>
        <item name="android:padding">4dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:background">@drawable/close_button_background</item>
        <item name="android:textColor">#666666</item>
        <item name="android:gravity">center</item>
    </style>

</resources>
