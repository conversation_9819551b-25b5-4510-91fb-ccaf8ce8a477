<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/tab_selected_background" />
            <corners android:radius="4dp" />
            <stroke android:width="1dp" android:color="@color/colorPrimary" />
        </shape>
    </item>
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/colorPrimary" />
            <corners android:radius="4dp" />
            <stroke android:width="1dp" android:color="@color/colorPrimaryDark" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/tab_unselected_background" />
            <corners android:radius="4dp" />
            <stroke android:width="1dp" android:color="#CCCCCC" />
        </shape>
    </item>
</selector>
