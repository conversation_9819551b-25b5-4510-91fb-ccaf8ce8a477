@echo off
echo ========================================
echo Android 开发环境检查和设置脚本
echo ========================================

echo.
echo 1. 检查 Java 安装...
java -version
if %ERRORLEVEL% NEQ 0 (
    echo [错误] Java 未安装或未配置到 PATH
    echo 请安装 JDK 11 或更高版本
    echo 下载地址: https://adoptium.net/
    pause
    exit /b 1
) else (
    echo [成功] Java 已安装
)

echo.
echo 2. 检查 ADB 安装...
adb version
if %ERRORLEVEL% NEQ 0 (
    echo [错误] ADB 未安装或未配置到 PATH
    echo 请安装 Android SDK Platform Tools
    echo 下载地址: https://developer.android.com/studio/releases/platform-tools
    pause
    exit /b 1
) else (
    echo [成功] ADB 已安装
)

echo.
echo 3. 检查环境变量...
if "%ANDROID_HOME%"=="" (
    echo [警告] ANDROID_HOME 环境变量未设置
    echo 请设置 ANDROID_HOME 指向 Android SDK 目录
    echo 例如: C:\Users\<USER>\AppData\Local\Android\Sdk
) else (
    echo [成功] ANDROID_HOME = %ANDROID_HOME%
)

if "%JAVA_HOME%"=="" (
    echo [警告] JAVA_HOME 环境变量未设置
    echo 建议设置 JAVA_HOME 指向 JDK 安装目录
) else (
    echo [成功] JAVA_HOME = %JAVA_HOME%
)

echo.
echo 4. 检查项目文件...
if not exist "app\build.gradle" (
    echo [错误] 找不到 app\build.gradle 文件
    echo 请确保在正确的项目目录中运行此脚本
    pause
    exit /b 1
) else (
    echo [成功] 项目文件存在
)

echo.
echo 5. 尝试下载 Gradle Wrapper...
if not exist "gradle\wrapper\gradle-wrapper.jar" (
    echo 正在下载 Gradle Wrapper...
    powershell -Command "try { Invoke-WebRequest -Uri 'https://github.com/gradle/gradle/raw/v8.1.0/gradle/wrapper/gradle-wrapper.jar' -OutFile 'gradle/wrapper/gradle-wrapper.jar' -TimeoutSec 30; Write-Host '[成功] Gradle Wrapper 下载完成' } catch { Write-Host '[错误] Gradle Wrapper 下载失败:' $_.Exception.Message }"
) else (
    echo [成功] Gradle Wrapper 已存在
)

echo.
echo 6. 测试 Gradle Wrapper...
if exist "gradle\wrapper\gradle-wrapper.jar" (
    echo 正在测试 Gradle...
    .\gradlew.bat --version
    if %ERRORLEVEL% EQU 0 (
        echo [成功] Gradle 工作正常
    ) else (
        echo [错误] Gradle 测试失败
    )
) else (
    echo [跳过] Gradle Wrapper 不可用
)

echo.
echo 7. 检查连接的设备...
echo 连接的 Android 设备:
adb devices

echo.
echo ========================================
echo 环境检查完成
echo ========================================
echo.
echo 如果所有检查都通过，您可以尝试构建项目:
echo   .\gradlew.bat assembleDebug
echo.
echo 如果遇到问题，请参考 SETUP_GUIDE.md 文件
echo.
pause
