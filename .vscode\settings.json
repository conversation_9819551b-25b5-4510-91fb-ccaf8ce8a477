{"java.configuration.updateBuildConfiguration": "automatic", "java.compile.nullAnalysis.mode": "automatic", "java.debug.settings.onBuildFailureProceed": true, "java.import.gradle.enabled": true, "java.import.gradle.wrapper.enabled": true, "java.import.gradle.java.home": null, "java.import.gradle.offline.enabled": false, "java.import.gradle.arguments": "", "java.import.gradle.jvmArguments": "", "java.import.gradle.user.home": null, "java.import.gradle.version": null, "java.import.gradle.home": null, "java.saveActions.organizeImports": true, "java.format.enabled": true, "java.format.settings.url": null, "java.format.settings.profile": null, "java.format.comments.enabled": true, "java.codeGeneration.insertionLocation": "afterCursor", "java.codeGeneration.generateComments": true, "java.codeGeneration.useBlocks": false, "java.sources.organizeImports.starThreshold": 99, "java.sources.organizeImports.staticStarThreshold": 99, "files.exclude": {"**/.gradle": true, "**/build": true, "**/.idea": true, "**/*.iml": true, "**/.classpath": true, "**/.project": true, "**/.settings": true, "**/.factorypath": true}, "files.associations": {"*.gradle": "groovy", "gradle.properties": "properties"}, "emulator.emulatorPath": "emulator", "emulator.adbPath": "adb"}