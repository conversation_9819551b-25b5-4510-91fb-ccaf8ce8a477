@echo off
echo ========================================
echo 打开 APK 文件夹
echo ========================================

set APK_PATH=%~dp0app\build\outputs\apk\debug

echo APK 文件位置: %APK_PATH%

if exist "%APK_PATH%\app-debug.apk" (
    echo [SUCCESS] 找到 APK 文件: app-debug.apk
    echo 文件大小: 
    dir "%APK_PATH%\app-debug.apk"
    echo.
    echo 正在打开文件夹...
    explorer "%APK_PATH%"
) else (
    echo [ERROR] 未找到 APK 文件
    echo 请先运行构建命令: .\gradlew.bat assembleDebug
)

echo.
echo 按任意键继续...
pause >nul
