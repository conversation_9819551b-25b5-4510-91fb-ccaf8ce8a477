{"version": "2.0.0", "tasks": [{"label": "Android: Build Debug APK", "type": "shell", "command": "./gradlew", "args": ["assembleDebug"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Android: Build Release APK", "type": "shell", "command": "./gradlew", "args": ["assembleRelease"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Android: Clean Project", "type": "shell", "command": "./gradlew", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Android: Install Debug APK", "type": "shell", "command": "./gradlew", "args": ["installDebug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "dependsOn": "Android: Build Debug APK"}, {"label": "Android: Uninstall App", "type": "shell", "command": "./gradlew", "args": ["uninstallAll"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "ADB: List Devices", "type": "shell", "command": "adb", "args": ["devices"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "ADB: Start Logcat", "type": "shell", "command": "adb", "args": ["logcat"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}]}