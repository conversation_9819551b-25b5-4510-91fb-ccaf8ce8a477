# 安装 Java 11 的 PowerShell 脚本

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Java 11 安装脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# 检查是否以管理员身份运行
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "[警告] 建议以管理员身份运行此脚本以获得最佳效果" -ForegroundColor Yellow
    Write-Host "您可以继续，但可能需要手动设置环境变量" -ForegroundColor Yellow
    Write-Host ""
}

# 检查 Chocolatey 是否已安装
Write-Host "1. 检查 Chocolatey..." -ForegroundColor Yellow
try {
    $chocoVersion = choco --version 2>$null
    Write-Host "[成功] Chocolatey 已安装: $chocoVersion" -ForegroundColor Green
} catch {
    Write-Host "[信息] Chocolatey 未安装，正在安装..." -ForegroundColor Yellow
    try {
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        Write-Host "[成功] Chocolatey 安装完成" -ForegroundColor Green
    } catch {
        Write-Host "[错误] Chocolatey 安装失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "请手动安装 Chocolatey 或 Java 11" -ForegroundColor Red
        exit 1
    }
}

# 检查 Java 11 是否已安装
Write-Host ""
Write-Host "2. 检查 Java 11..." -ForegroundColor Yellow
$java11Paths = @(
    "C:\Program Files\Eclipse Adoptium\jdk-11*",
    "C:\Program Files\Java\jdk-11*",
    "C:\Program Files\OpenJDK\jdk-11*"
)

$java11Found = $false
$java11Path = ""

foreach ($path in $java11Paths) {
    $found = Get-ChildItem $path -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($found) {
        $java11Found = $true
        $java11Path = $found.FullName
        Write-Host "[成功] 找到 Java 11: $java11Path" -ForegroundColor Green
        break
    }
}

if (-not $java11Found) {
    Write-Host "[信息] Java 11 未找到，正在安装..." -ForegroundColor Yellow
    try {
        choco install openjdk11 -y
        Write-Host "[成功] Java 11 安装完成" -ForegroundColor Green
        
        # 重新查找 Java 11 路径
        foreach ($path in $java11Paths) {
            $found = Get-ChildItem $path -ErrorAction SilentlyContinue | Select-Object -First 1
            if ($found) {
                $java11Path = $found.FullName
                break
            }
        }
    } catch {
        Write-Host "[错误] Java 11 安装失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "请手动下载并安装 Java 11 from: https://adoptium.net/" -ForegroundColor Red
        exit 1
    }
}

# 设置环境变量
Write-Host ""
Write-Host "3. 设置环境变量..." -ForegroundColor Yellow

if ($java11Path) {
    # 设置当前会话的环境变量
    $env:JAVA_HOME = $java11Path
    $env:PATH = "$java11Path\bin;$env:PATH"
    
    Write-Host "[成功] 当前会话的 JAVA_HOME 设置为: $java11Path" -ForegroundColor Green
    
    # 尝试永久设置环境变量（需要管理员权限）
    if ($isAdmin) {
        try {
            [Environment]::SetEnvironmentVariable("JAVA_HOME", $java11Path, "Machine")
            Write-Host "[成功] 系统环境变量 JAVA_HOME 已设置" -ForegroundColor Green
        } catch {
            Write-Host "[警告] 无法设置系统环境变量，请手动设置" -ForegroundColor Yellow
        }
    } else {
        Write-Host "[信息] 要永久设置环境变量，请以管理员身份运行:" -ForegroundColor Yellow
        Write-Host "  [Environment]::SetEnvironmentVariable('JAVA_HOME', '$java11Path', 'Machine')" -ForegroundColor White
    }
} else {
    Write-Host "[错误] 无法找到 Java 11 安装路径" -ForegroundColor Red
    exit 1
}

# 验证 Java 安装
Write-Host ""
Write-Host "4. 验证 Java 安装..." -ForegroundColor Yellow
try {
    $javaVersion = & "$java11Path\bin\java.exe" -version 2>&1
    Write-Host "[成功] Java 验证通过:" -ForegroundColor Green
    Write-Host $javaVersion[0] -ForegroundColor Gray
} catch {
    Write-Host "[错误] Java 验证失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Java 11 安装和配置完成!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "下一步:" -ForegroundColor Green
Write-Host "1. 重新启动 PowerShell 或 VSCode" -ForegroundColor White
Write-Host "2. 运行: .\gradlew.bat clean" -ForegroundColor White
Write-Host "3. 运行: .\gradlew.bat assembleDebug" -ForegroundColor White

Write-Host ""
Write-Host "如果仍有问题，请手动设置环境变量:" -ForegroundColor Yellow
Write-Host "  JAVA_HOME = $java11Path" -ForegroundColor White

Write-Host ""
Write-Host "按任意键继续..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
