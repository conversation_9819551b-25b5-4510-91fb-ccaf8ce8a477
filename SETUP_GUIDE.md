# VSCode Android 开发环境配置指南

## 📋 环境要求

- Windows 10/11
- VSCode 最新版本
- 至少 8GB RAM
- 至少 10GB 可用磁盘空间

## 🛠️ 安装步骤

### 1. 安装 JDK

#### 方式1：使用 Chocolatey（推荐）
```powershell
# 以管理员身份运行 PowerShell
Set-ExecutionPolicy Bypass -Scope Process -Force
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# 安装 JDK 11
choco install openjdk11
```

#### 方式2：手动安装
1. 下载 OpenJDK 11: https://adoptium.net/
2. 安装并设置环境变量 `JAVA_HOME`
3. 将 `%JAVA_HOME%\bin` 添加到 PATH

### 2. 安装 Android SDK

#### 方式1：安装 Android Studio（推荐）
1. 下载 Android Studio: https://developer.android.com/studio
2. 安装时选择 "Standard" 安装类型
3. 安装完成后，Android SDK 会自动配置

#### 方式2：只安装 Command Line Tools
1. 下载 Command Line Tools: https://developer.android.com/studio#command-tools
2. 解压到 `C:\Android\cmdline-tools\latest\`
3. 设置环境变量：
   ```
   ANDROID_HOME=C:\Android
   ANDROID_SDK_ROOT=%ANDROID_HOME%
   ```
4. 添加到 PATH：
   ```
   %ANDROID_HOME%\cmdline-tools\latest\bin
   %ANDROID_HOME%\platform-tools
   %ANDROID_HOME%\emulator
   ```

### 3. 安装 Android SDK 组件

```bash
# 使用 sdkmanager 安装必要组件
sdkmanager "platform-tools" "platforms;android-33" "build-tools;33.0.0"
sdkmanager "system-images;android-33;google_apis;x86_64"
sdkmanager "emulator"
```

### 4. 安装 VSCode 扩展

在 VSCode 中安装以下扩展：

```json
{
  "必需扩展": [
    "vscjava.vscode-java-pack",           // Java 开发包
    "redhat.java",                        // Java 语言支持
    "vscjava.vscode-java-debug",          // Java 调试
    "vscjava.vscode-gradle",              // Gradle 支持
  ],
  "推荐扩展": [
    "adelphes.android-dev-ext",           // Android 开发扩展
    "DiemasMichiels.emulate",             // Android 模拟器
    "ms-vscode.vscode-json",              // JSON 支持
    "ms-vscode.powershell"                // PowerShell 支持
  ]
}
```

### 5. 验证安装

在终端中运行以下命令验证安装：

```bash
# 检查 Java
java -version

# 检查 Android SDK
adb version

# 检查 Gradle（在项目目录中）
.\gradlew.bat --version
```

## 🚀 项目构建

### 1. 克隆或下载项目
```bash
git clone <your-repo-url>
cd android-doc-preview
```

### 2. 构建项目
```bash
# 清理项目
.\gradlew.bat clean

# 构建 Debug APK
.\gradlew.bat assembleDebug

# 安装到设备
.\gradlew.bat installDebug
```

### 3. VSCode 任务

项目已配置了 VSCode 任务，可以通过以下方式使用：

1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "Tasks: Run Task"
3. 选择以下任务之一：
   - `Android: Build Debug APK`
   - `Android: Build Release APK`
   - `Android: Clean Project`
   - `Android: Install Debug APK`
   - `ADB: List Devices`

## 🔧 常见问题解决

### 问题1：JAVA_HOME 未设置
```bash
# 检查 JAVA_HOME
echo $env:JAVA_HOME

# 设置 JAVA_HOME（临时）
$env:JAVA_HOME = "C:\Program Files\Java\jdk-11.0.x"
```

### 问题2：Android SDK 路径问题
```bash
# 检查 ANDROID_HOME
echo $env:ANDROID_HOME

# 设置 ANDROID_HOME（临时）
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
```

### 问题3：Gradle 下载超时
```bash
# 使用代理或更换网络
# 或者手动下载 Gradle 并解压到 ~/.gradle/wrapper/dists/
```

### 问题4：设备连接问题
```bash
# 启用开发者选项和 USB 调试
adb devices

# 如果设备未显示，尝试重启 ADB
adb kill-server
adb start-server
```

## 📱 创建虚拟设备

### 使用 Android Studio
1. 打开 Android Studio
2. 点击 "AVD Manager"
3. 创建新的虚拟设备
4. 选择设备型号和系统镜像

### 使用命令行
```bash
# 创建 AVD
avdmanager create avd -n "Pixel_4_API_33" -k "system-images;android-33;google_apis;x86_64"

# 启动模拟器
emulator -avd Pixel_4_API_33
```

## 🎯 开发工作流

### 1. 日常开发
```bash
# 启动模拟器或连接真机
adb devices

# 构建并安装应用
.\gradlew.bat installDebug

# 查看日志
adb logcat
```

### 2. 调试
1. 在 VSCode 中设置断点
2. 使用 Java 调试器连接到应用
3. 或使用 `adb logcat` 查看日志

### 3. 发布
```bash
# 构建 Release APK
.\gradlew.bat assembleRelease

# APK 位置：app/build/outputs/apk/release/
```

## 📚 有用的命令

```bash
# Gradle 命令
.\gradlew.bat tasks                    # 查看所有任务
.\gradlew.bat clean                    # 清理项目
.\gradlew.bat build                    # 构建项目
.\gradlew.bat assembleDebug           # 构建 Debug APK
.\gradlew.bat assembleRelease         # 构建 Release APK
.\gradlew.bat installDebug            # 安装 Debug APK

# ADB 命令
adb devices                           # 列出连接的设备
adb install app.apk                   # 安装 APK
adb uninstall com.package.name        # 卸载应用
adb logcat                           # 查看日志
adb shell                            # 进入设备 shell

# 模拟器命令
emulator -list-avds                   # 列出所有 AVD
emulator -avd <avd_name>             # 启动指定 AVD
```

## 🔗 有用的链接

- [Android 开发者文档](https://developer.android.com/docs)
- [Gradle 用户指南](https://docs.gradle.org/current/userguide/userguide.html)
- [VSCode Java 扩展文档](https://code.visualstudio.com/docs/java/java-tutorial)
- [腾讯 TBS SDK 文档](https://x5.tencent.com/docs/index.html)

## 💡 提示

1. **性能优化**：增加 Gradle 内存分配
   ```properties
   # gradle.properties
   org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=512m
   ```

2. **网络问题**：配置代理或使用国内镜像
   ```gradle
   // build.gradle
   repositories {
       maven { url 'https://maven.aliyun.com/repository/google' }
       maven { url 'https://maven.aliyun.com/repository/central' }
   }
   ```

3. **快速构建**：启用 Gradle 守护进程
   ```properties
   # gradle.properties
   org.gradle.daemon=true
   org.gradle.parallel=true
   ```
