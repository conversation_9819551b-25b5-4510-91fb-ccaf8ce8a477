@echo off
echo ========================================
echo Creating Android API 30 Structure
echo ========================================

set SDK_PATH=D:\my-soft\android-sdk
set API30_PATH=%SDK_PATH%\platforms\android-30
set BUILD_TOOLS_PATH=%SDK_PATH%\build-tools\30.0.3
set API36_PATH=%SDK_PATH%\platforms\android-36
set BUILD_TOOLS_36=%SDK_PATH%\build-tools\36.0.0

echo.
echo 1. Creating API 30 platform directory...
mkdir "%API30_PATH%" 2>nul

echo Creating source.properties for API 30...
echo Pkg.Desc=Android SDK Platform 30 > "%API30_PATH%\source.properties"
echo Pkg.UserSrc=false >> "%API30_PATH%\source.properties"
echo Pkg.Revision=3 >> "%API30_PATH%\source.properties"
echo AndroidVersion.ApiLevel=30 >> "%API30_PATH%\source.properties"
echo AndroidVersion.CodeName= >> "%API30_PATH%\source.properties"

echo.
echo 2. Copying android.jar from API 36...
if exist "%API36_PATH%\android.jar" (
    copy "%API36_PATH%\android.jar" "%API30_PATH%\android.jar" >nul
    echo [SUCCESS] Copied android.jar
) else (
    echo [WARNING] API 36 android.jar not found
)

echo.
echo 3. Creating Build Tools 30.0.3 directory...
mkdir "%BUILD_TOOLS_PATH%" 2>nul

echo Creating source.properties for Build Tools...
echo Pkg.Desc=Android SDK Build-Tools 30.0.3 > "%BUILD_TOOLS_PATH%\source.properties"
echo Pkg.UserSrc=false >> "%BUILD_TOOLS_PATH%\source.properties"
echo Pkg.Revision=30.0.3 >> "%BUILD_TOOLS_PATH%\source.properties"

echo.
echo 4. Copying build tools from 36.0.0...
if exist "%BUILD_TOOLS_36%" (
    copy "%BUILD_TOOLS_36%\aapt.exe" "%BUILD_TOOLS_PATH%\aapt.exe" >nul 2>&1
    copy "%BUILD_TOOLS_36%\aapt2.exe" "%BUILD_TOOLS_PATH%\aapt2.exe" >nul 2>&1
    copy "%BUILD_TOOLS_36%\aidl.exe" "%BUILD_TOOLS_PATH%\aidl.exe" >nul 2>&1
    copy "%BUILD_TOOLS_36%\zipalign.exe" "%BUILD_TOOLS_PATH%\zipalign.exe" >nul 2>&1
    copy "%BUILD_TOOLS_36%\apksigner.bat" "%BUILD_TOOLS_PATH%\apksigner.bat" >nul 2>&1
    copy "%BUILD_TOOLS_36%\d8.bat" "%BUILD_TOOLS_PATH%\d8.bat" >nul 2>&1
    
    echo [SUCCESS] Copied build tools
    
    REM Create dx.bat wrapper
    echo @echo off > "%BUILD_TOOLS_PATH%\dx.bat"
    echo "%%~dp0d8.bat" %%* >> "%BUILD_TOOLS_PATH%\dx.bat"
    echo [SUCCESS] Created dx.bat wrapper
    
    REM Copy lib directory
    if exist "%BUILD_TOOLS_36%\lib" (
        xcopy "%BUILD_TOOLS_36%\lib" "%BUILD_TOOLS_PATH%\lib" /E /I /Q >nul 2>&1
        echo [SUCCESS] Copied lib directory
    )
) else (
    echo [WARNING] Build Tools 36.0.0 not found
)

echo.
echo ========================================
echo API 30 Structure Creation Complete
echo ========================================

echo.
echo Created:
echo   Platform: %API30_PATH%
echo   Build Tools: %BUILD_TOOLS_PATH%

echo.
echo You can now try building the project:
echo   .\gradlew.bat clean assembleDebug

echo.
echo Press any key to continue...
pause >nul
