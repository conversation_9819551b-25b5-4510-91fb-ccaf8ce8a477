@echo off
echo ========================================
echo Android Multi-Document Preview Build
echo ========================================

echo.
echo 1. Setting up environment...

REM Set Java 8 environment
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-8.0.442.6-hotspot
set PATH=%JAVA_HOME%\bin;%PATH%

REM Set Android SDK environment
set ANDROID_HOME=D:\my-soft\android-sdk
set ANDROID_SDK_ROOT=%ANDROID_HOME%
set PATH=%ANDROID_HOME%\platform-tools;%ANDROID_HOME%\tools;%PATH%

echo [SUCCESS] Environment configured:
echo   JAVA_HOME = %JAVA_HOME%
echo   ANDROID_HOME = %ANDROID_HOME%

echo.
echo 2. Verifying Java installation...
java -version
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Java verification failed
    pause
    exit /b 1
)

echo.
echo 3. Checking Android SDK...
if not exist "%ANDROID_HOME%\platform-tools\adb.exe" (
    echo [WARNING] Android SDK not fully installed
    echo Please install Android SDK using Android Studio or command line tools
    echo.
    echo For now, attempting to build with minimal configuration...
)

echo.
echo 4. Building project...
echo Running: gradlew.bat clean assembleDebug

.\gradlew.bat clean assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo APK location: app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo To install on device:
    echo   adb install app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo Or use:
    echo   .\gradlew.bat installDebug
) else (
    echo.
    echo ========================================
    echo BUILD FAILED
    echo ========================================
    echo.
    echo Common solutions:
    echo 1. Install complete Android SDK via Android Studio
    echo 2. Check ANDROID_HOME path: %ANDROID_HOME%
    echo 3. Ensure all required SDK components are installed
    echo.
    echo See COMPILATION_SUCCESS.md for detailed instructions
)

echo.
echo Press any key to continue...
pause >nul
