Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Android Development Environment Check" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "1. Checking Java installation..." -ForegroundColor Yellow
try {
    $javaVersion = java -version 2>&1
    Write-Host "[SUCCESS] Java is installed" -ForegroundColor Green
    Write-Host $javaVersion[0] -ForegroundColor Gray
} catch {
    Write-Host "[ERROR] Java is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install JDK 11 or higher from: https://adoptium.net/" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "2. Checking ADB installation..." -ForegroundColor Yellow
try {
    $adbVersion = adb version 2>&1
    Write-Host "[SUCCESS] ADB is installed" -ForegroundColor Green
    Write-Host $adbVersion[0] -ForegroundColor Gray
} catch {
    Write-Host "[ERROR] ADB is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Android SDK Platform Tools" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "3. Checking environment variables..." -ForegroundColor Yellow
if ($env:ANDROID_HOME) {
    Write-Host "[SUCCESS] ANDROID_HOME = $env:ANDROID_HOME" -ForegroundColor Green
} else {
    Write-Host "[WARNING] ANDROID_HOME environment variable is not set" -ForegroundColor Yellow
    Write-Host "Please set ANDROID_HOME to your Android SDK directory" -ForegroundColor Yellow
}

if ($env:JAVA_HOME) {
    Write-Host "[SUCCESS] JAVA_HOME = $env:JAVA_HOME" -ForegroundColor Green
} else {
    Write-Host "[WARNING] JAVA_HOME environment variable is not set" -ForegroundColor Yellow
    Write-Host "It's recommended to set JAVA_HOME to your JDK directory" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "4. Checking project files..." -ForegroundColor Yellow
if (Test-Path "app\build.gradle") {
    Write-Host "[SUCCESS] Project files exist" -ForegroundColor Green
} else {
    Write-Host "[ERROR] Cannot find app\build.gradle file" -ForegroundColor Red
    Write-Host "Please make sure you're running this script in the correct project directory" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "5. Checking Gradle Wrapper..." -ForegroundColor Yellow
if (Test-Path "gradle\wrapper\gradle-wrapper.jar") {
    Write-Host "[SUCCESS] Gradle Wrapper exists" -ForegroundColor Green
} else {
    Write-Host "Downloading Gradle Wrapper..." -ForegroundColor Yellow
    try {
        New-Item -ItemType Directory -Force -Path "gradle\wrapper" | Out-Null
        Invoke-WebRequest -Uri "https://github.com/gradle/gradle/raw/v8.1.0/gradle/wrapper/gradle-wrapper.jar" -OutFile "gradle\wrapper\gradle-wrapper.jar" -TimeoutSec 30
        Write-Host "[SUCCESS] Gradle Wrapper downloaded" -ForegroundColor Green
    } catch {
        Write-Host "[ERROR] Failed to download Gradle Wrapper: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "6. Testing Gradle..." -ForegroundColor Yellow
if (Test-Path "gradle\wrapper\gradle-wrapper.jar") {
    try {
        Write-Host "Running Gradle version check..." -ForegroundColor Gray
        $gradleOutput = & ".\gradlew.bat" --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "[SUCCESS] Gradle is working" -ForegroundColor Green
        } else {
            Write-Host "[ERROR] Gradle test failed" -ForegroundColor Red
            Write-Host $gradleOutput -ForegroundColor Red
        }
    } catch {
        Write-Host "[ERROR] Failed to run Gradle: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "[SKIP] Gradle Wrapper not available" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "7. Checking connected devices..." -ForegroundColor Yellow
Write-Host "Connected Android devices:" -ForegroundColor Gray
adb devices

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Environment check completed" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "If all checks passed, you can try building the project:" -ForegroundColor Green
Write-Host "  .\gradlew.bat assembleDebug" -ForegroundColor White
Write-Host ""
Write-Host "If you encounter issues, please refer to SETUP_GUIDE.md" -ForegroundColor Yellow
Write-Host ""

Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
