import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Button;
import android.widget.ScrollView;
import androidx.appcompat.app.AppCompatActivity;
import com.tencent.smtt.sdk.TbsReaderView;
import java.util.ArrayList;
import java.util.List;

public class DocumentPreviewActivity extends AppCompatActivity {
    private TbsReaderView mTbsReaderView;
    private LinearLayout documentTabsLayout;
    private ScrollView tabScrollView;
    private List<DocumentInfo> documentList;
    private int currentDocumentIndex = 0;

    // 文档信息类
    private static class DocumentInfo {
        String filePath;
        String tempPath;
        String fileName;

        DocumentInfo(String filePath, String tempPath, String fileName) {
            this.filePath = filePath;
            this.tempPath = tempPath;
            this.fileName = fileName;
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 创建主布局
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);

        // 创建标签页滚动视图
        tabScrollView = new ScrollView(this);
        tabScrollView.setLayoutParams(new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 150));

        // 创建标签页容器
        documentTabsLayout = new LinearLayout(this);
        documentTabsLayout.setOrientation(LinearLayout.HORIZONTAL);
        documentTabsLayout.setPadding(16, 8, 16, 8);
        tabScrollView.addView(documentTabsLayout);

        // 创建文档预览视图
        mTbsReaderView = new TbsReaderView(this, readerCallback);
        LinearLayout.LayoutParams readerParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 0, 1.0f);
        mTbsReaderView.setLayoutParams(readerParams);

        // 添加到主布局
        mainLayout.addView(tabScrollView);
        mainLayout.addView(mTbsReaderView);

        setContentView(mainLayout);

        // 初始化文档列表
        initializeDocuments();

        // 创建标签页
        createDocumentTabs();

        // 预览第一个文档
        if (!documentList.isEmpty()) {
            previewDocument(0);
        }
    }

    private void initializeDocuments() {
        documentList = new ArrayList<>();

        // 从Intent获取文档信息
        Intent intent = getIntent();
        if (intent.hasExtra("documentPaths")) {
            // 多文档模式
            ArrayList<String> filePaths = intent.getStringArrayListExtra("documentPaths");
            ArrayList<String> tempPaths = intent.getStringArrayListExtra("documentTempPaths");
            ArrayList<String> fileNames = intent.getStringArrayListExtra("documentNames");

            if (filePaths != null && tempPaths != null && fileNames != null) {
                for (int i = 0; i < filePaths.size(); i++) {
                    documentList.add(new DocumentInfo(
                        filePaths.get(i),
                        tempPaths.get(i),
                        fileNames.get(i)
                    ));
                }
            }
        } else {
            // 单文档模式（向后兼容）
            String filePath = intent.getStringExtra("filePath");
            String tempPath = intent.getStringExtra("tempPath");
            String fileName = intent.getStringExtra("fileName");

            if (filePath != null && tempPath != null) {
                if (fileName == null) {
                    fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
                }
                documentList.add(new DocumentInfo(filePath, tempPath, fileName));
            }
        }
    }

    private void createDocumentTabs() {
        documentTabsLayout.removeAllViews();

        for (int i = 0; i < documentList.size(); i++) {
            final int index = i;
            DocumentInfo doc = documentList.get(i);

            // 创建标签页按钮
            Button tabButton = new Button(this);
            tabButton.setText(doc.fileName);
            tabButton.setPadding(24, 12, 24, 12);
            tabButton.setTextSize(14);

            // 设置标签页样式
            LinearLayout.LayoutParams tabParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
            tabParams.setMargins(4, 0, 4, 0);
            tabButton.setLayoutParams(tabParams);

            // 设置点击事件
            tabButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    switchToDocument(index);
                }
            });

            documentTabsLayout.addView(tabButton);

            // 添加关闭按钮
            if (documentList.size() > 1) {
                Button closeButton = new Button(this);
                closeButton.setText("×");
                closeButton.setTextSize(16);
                closeButton.setPadding(8, 12, 8, 12);

                LinearLayout.LayoutParams closeParams = new LinearLayout.LayoutParams(
                    60, LinearLayout.LayoutParams.WRAP_CONTENT);
                closeParams.setMargins(0, 0, 8, 0);
                closeButton.setLayoutParams(closeParams);

                closeButton.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        closeDocument(index);
                    }
                });

                documentTabsLayout.addView(closeButton);
            }
        }

        updateTabStyles();
    }

    private void updateTabStyles() {
        for (int i = 0; i < documentTabsLayout.getChildCount(); i += 2) {
            Button tabButton = (Button) documentTabsLayout.getChildAt(i);
            if (i / 2 == currentDocumentIndex) {
                // 当前选中的标签页
                tabButton.setBackgroundColor(0xFF2196F3);
                tabButton.setTextColor(0xFFFFFFFF);
            } else {
                // 未选中的标签页
                tabButton.setBackgroundColor(0xFFE0E0E0);
                tabButton.setTextColor(0xFF000000);
            }
        }
    }

    private void switchToDocument(int index) {
        if (index >= 0 && index < documentList.size() && index != currentDocumentIndex) {
            currentDocumentIndex = index;
            previewDocument(index);
            updateTabStyles();
        }
    }

    private void closeDocument(int index) {
        if (documentList.size() <= 1) {
            return; // 不能关闭最后一个文档
        }

        documentList.remove(index);

        // 调整当前文档索引
        if (currentDocumentIndex >= index) {
            currentDocumentIndex = Math.max(0, currentDocumentIndex - 1);
        }

        // 重新创建标签页
        createDocumentTabs();

        // 预览当前文档
        if (!documentList.isEmpty()) {
            previewDocument(currentDocumentIndex);
        } else {
            finish(); // 如果没有文档了，关闭Activity
        }
    }

    private void previewDocument(int index) {
        if (index < 0 || index >= documentList.size()) {
            return;
        }

        DocumentInfo doc = documentList.get(index);

        // 预览文档
        Bundle localBundle = new Bundle();
        localBundle.putString("filePath", doc.filePath);
        localBundle.putString("tempPath", doc.tempPath);
        mTbsReaderView.launchFileReader(localBundle);

        currentDocumentIndex = index;
    }

    private TbsReaderView.ReaderCallback readerCallback = new TbsReaderView.ReaderCallback() {
        public void onCallBackAction(Integer integer, Object o, Object o1) {
            // 处理回调
            if (integer != null) {
                switch (integer) {
                    case 1: // 文档加载完成
                        // 可以在这里添加加载完成的处理逻辑
                        break;
                    case 2: // 文档加载失败
                        // 可以在这里添加加载失败的处理逻辑
                        break;
                    default:
                        break;
                }
            }
        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mTbsReaderView != null) {
            mTbsReaderView.onStop();
        }
    }
}