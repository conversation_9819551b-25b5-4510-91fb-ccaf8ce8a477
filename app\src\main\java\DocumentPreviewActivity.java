public class DocumentPreviewActivity extends AppCompatActivity {
    private TbsReaderView mTbsReaderView;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        mTbsReaderView = new TbsReaderView(this, readerCallback);
        setContentView(mTbsReaderView);
        
        // 预览文档
        Bundle localBundle = new Bundle();
        localBundle.putString("filePath", filePath);
        localBundle.putString("tempPath", tempPath);
        mTbsReaderView.launchFileReader(localBundle);
    }
    
    private TbsReaderView.ReaderCallback readerCallback = new TbsReaderView.ReaderCallback() {
        public void onCallBackAction(Integer integer, Object o, Object o1) {
            // 处理回调
        }
    };
}