# Android 多文档预览应用

这是一个基于腾讯 TBS SDK 的 Android 多文档预览应用，支持同时预览多个文档并在它们之间快速切换。

## 功能特性

### 🔥 核心功能
- **多文档预览**: 同时打开和预览多个文档
- **标签页切换**: 类似浏览器的标签页界面，方便在文档间切换
- **文档管理**: 支持添加、移除文档
- **格式支持**: 支持 PDF、Word、Excel、PowerPoint 等多种格式

### 📱 支持的文件格式
- **PDF**: .pdf
- **Word**: .doc, .docx
- **Excel**: .xls, .xlsx
- **PowerPoint**: .ppt, .pptx
- **文本**: .txt, .rtf
- **电子书**: .epub, .mobi, .fb2, .chm

### 🎨 用户界面
- 顶部标签页显示所有打开的文档
- 点击标签页切换文档
- 每个标签页都有关闭按钮（最后一个文档除外）
- 响应式布局，适配不同屏幕尺寸

## 使用方法

### 基本使用

1. **启动应用**: 打开应用后会看到主界面
2. **选择文档**: 点击"选择文档文件"按钮选择要预览的文档
3. **添加多个文档**: 可以多次选择文件或一次选择多个文件
4. **开始预览**: 点击"开始预览"按钮打开多文档预览界面
5. **切换文档**: 在预览界面点击顶部的标签页切换文档
6. **关闭文档**: 点击标签页旁边的"×"按钮关闭文档

### 编程使用

#### 1. 使用 DocumentManager 类

```java
// 创建文档管理器
DocumentManager documentManager = new DocumentManager(context);

// 添加单个文档
documentManager.addDocument(filePath, tempPath, fileName);

// 添加多个文档
List<DocumentManager.Document> documents = new ArrayList<>();
documents.add(new DocumentManager.Document(filePath1, tempPath1, fileName1));
documents.add(new DocumentManager.Document(filePath2, tempPath2, fileName2));
documentManager.addDocuments(documents);

// 启动多文档预览
documentManager.startMultiDocumentPreview();
```

#### 2. 直接启动预览

```java
// 单文档预览
DocumentManager.startSingleDocumentPreview(context, filePath, tempPath, fileName);

// 多文档预览
Intent intent = new Intent(context, DocumentPreviewActivity.class);
intent.putStringArrayListExtra("documentPaths", filePaths);
intent.putStringArrayListExtra("documentTempPaths", tempPaths);
intent.putStringArrayListExtra("documentNames", fileNames);
context.startActivity(intent);
```

## 项目结构

```
app/src/main/java/
├── MainActivity.java              # 主界面，演示多文档预览功能
├── DocumentPreviewActivity.java   # 文档预览界面，支持多文档标签页
└── DocumentManager.java           # 文档管理器，提供便捷的API

app/src/main/res/
├── values/
│   ├── strings.xml               # 字符串资源
│   ├── colors.xml                # 颜色资源
│   └── styles.xml                # 样式资源
├── drawable/                     # 图形资源
│   ├── tab_button_selector.xml   # 标签页按钮样式
│   └── close_button_background.xml # 关闭按钮样式
└── color/
    └── tab_text_selector.xml     # 标签页文字颜色选择器
```

## 核心类说明

### DocumentPreviewActivity
- 主要的文档预览界面
- 支持多文档标签页显示
- 处理文档切换和关闭逻辑
- 基于腾讯 TBS SDK 进行文档渲染

### DocumentManager
- 文档管理工具类
- 提供便捷的文档添加、移除、预览API
- 支持文件格式检查
- 简化多文档预览的启动流程

### DocumentManager.Document
- 文档信息封装类
- 包含文件路径、临时路径、文件名等信息
- 提供文件格式支持检查功能

## 依赖项

```gradle
dependencies {
    implementation 'com.tencent.tbs:tbssdk:44286'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.core:core:1.10.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'com.google.android.material:material:1.9.0'
}
```

## 权限要求

```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
```

## 注意事项

1. **TBS SDK 初始化**: 首次使用需要初始化腾讯 TBS SDK
2. **文件路径**: 确保文件路径正确且文件存在
3. **权限申请**: Android 6.0+ 需要动态申请存储权限
4. **内存管理**: 大文档可能占用较多内存，注意内存优化
5. **网络连接**: TBS SDK 可能需要网络连接进行初始化

## 扩展功能

可以基于现有代码扩展以下功能：

- **书签功能**: 为每个文档添加书签支持
- **搜索功能**: 在文档中搜索文本
- **注释功能**: 添加文档注释和标记
- **分享功能**: 分享文档或预览截图
- **最近文档**: 记录最近打开的文档
- **文档缩略图**: 在标签页显示文档缩略图

## 故障排除

### 常见问题

1. **文档无法打开**
   - 检查文件路径是否正确
   - 确认文件格式是否支持
   - 检查文件是否损坏

2. **标签页显示异常**
   - 检查文档名称是否过长
   - 确认屏幕方向变化处理

3. **内存不足**
   - 限制同时打开的文档数量
   - 优化大文档的处理

## 许可证

本项目基于 MIT 许可证开源。
