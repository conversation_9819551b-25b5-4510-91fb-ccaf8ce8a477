# 设置最小 Android SDK 的脚本

$sdkPath = "$env:USERPROFILE\AppData\Local\Android\Sdk"

Write-Host "Creating minimal Android SDK structure at: $sdkPath" -ForegroundColor Yellow

# 创建 SDK 目录结构
New-Item -ItemType Directory -Force -Path "$sdkPath\platform-tools" | Out-Null
New-Item -ItemType Directory -Force -Path "$sdkPath\platforms\android-30" | Out-Null
New-Item -ItemType Directory -Force -Path "$sdkPath\build-tools\30.0.3" | Out-Null

# 复制 ADB 到 platform-tools
$adbSource = "C:\ProgramData\chocolatey\bin\adb.exe"
if (Test-Path $adbSource) {
    Copy-Item $adbSource "$sdkPath\platform-tools\adb.exe" -Force
    Write-Host "Copied ADB to platform-tools" -ForegroundColor Green
}

# 创建基本的 source.properties 文件
@"
Pkg.Desc=Android SDK Platform-Tools
Pkg.UserSrc=false
Pkg.Revision=34.0.4
"@ | Out-File -FilePath "$sdkPath\platform-tools\source.properties" -Encoding UTF8

@"
Pkg.Desc=Android SDK Platform 30
Pkg.UserSrc=false
Pkg.Revision=3
AndroidVersion.ApiLevel=30
AndroidVersion.CodeName=
"@ | Out-File -FilePath "$sdkPath\platforms\android-30\source.properties" -Encoding UTF8

@"
Pkg.Desc=Android SDK Build-Tools 30.0.3
Pkg.UserSrc=false
Pkg.Revision=30.0.3
"@ | Out-File -FilePath "$sdkPath\build-tools\30.0.3\source.properties" -Encoding UTF8

Write-Host "Minimal Android SDK structure created successfully!" -ForegroundColor Green
Write-Host "SDK Path: $sdkPath" -ForegroundColor Cyan

# 设置环境变量
$env:ANDROID_HOME = $sdkPath
$env:ANDROID_SDK_ROOT = $sdkPath

Write-Host "Environment variables set:" -ForegroundColor Green
Write-Host "  ANDROID_HOME = $sdkPath" -ForegroundColor White
Write-Host "  ANDROID_SDK_ROOT = $sdkPath" -ForegroundColor White
