# 🚀 快速解决 Java 版本问题

## 🎯 问题总结
您当前使用的是 Java 24，但 Android 项目需要 Java 11 或更早版本。

## 💡 最简单的解决方案

### 方案1：手动下载并安装 Java 11（推荐）

1. **下载 Java 11**：
   - 访问：https://adoptium.net/temurin/releases/?version=11
   - 选择 Windows x64 版本
   - 下载 `.msi` 安装包

2. **安装 Java 11**：
   - 运行下载的 `.msi` 文件
   - 按默认设置安装
   - 记住安装路径（通常是 `C:\Program Files\Eclipse Adoptium\jdk-11.x.x-hotspot`）

3. **设置环境变量**：
   ```powershell
   # 临时设置（当前会话有效）
   $env:JAVA_HOME = "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"
   $env:PATH = "$env:JAVA_HOME\bin;$env:PATH"
   
   # 验证
   java -version
   ```

4. **永久设置环境变量**：
   - 右键"此电脑" → "属性" → "高级系统设置" → "环境变量"
   - 在"系统变量"中新建：
     - 变量名：`JAVA_HOME`
     - 变量值：`C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot`
   - 编辑 `Path` 变量，添加：`%JAVA_HOME%\bin`

### 方案2：使用便携版 Java 11

1. **下载便携版**：
   - 下载 Java 11 的 `.zip` 版本
   - 解压到 `C:\Java\jdk-11`

2. **设置环境变量**：
   ```powershell
   $env:JAVA_HOME = "C:\Java\jdk-11"
   $env:PATH = "C:\Java\jdk-11\bin;$env:PATH"
   ```

### 方案3：使用项目特定的 Java 版本

在项目根目录创建 `gradle.properties` 文件：
```properties
org.gradle.java.home=C:/Program Files/Eclipse Adoptium/jdk-*********-hotspot
```

## 🔧 验证和编译

1. **验证 Java 版本**：
   ```powershell
   java -version
   # 应该显示 Java 11.x.x
   ```

2. **清理并编译项目**：
   ```powershell
   # 清理项目
   .\gradlew.bat clean
   
   # 编译项目
   .\gradlew.bat assembleDebug
   ```

## 🎯 如果您想快速测试

**临时解决方案**（不需要安装）：

1. 下载 Java 11 便携版到项目目录
2. 在项目根目录创建 `use-java11.bat`：
   ```batch
   @echo off
   set JAVA_HOME=%~dp0java11
   set PATH=%JAVA_HOME%\bin;%PATH%
   echo Using Java 11 from: %JAVA_HOME%
   java -version
   .\gradlew.bat %*
   ```

3. 使用方式：
   ```batch
   .\use-java11.bat clean
   .\use-java11.bat assembleDebug
   ```

## 📋 下载链接

- **Java 11 LTS (推荐)**：https://adoptium.net/temurin/releases/?version=11
- **Oracle JDK 11**：https://www.oracle.com/java/technologies/javase/jdk11-archive-downloads.html
- **OpenJDK 11**：https://jdk.java.net/archive/

## ✅ 成功标志

编译成功后，您会看到：
```
BUILD SUCCESSFUL in Xs
```

然后可以在 `app/build/outputs/apk/debug/` 找到生成的 APK 文件。

## 🆘 如果仍有问题

1. **检查 Java 版本**：确保 `java -version` 显示 Java 11
2. **重启终端**：设置环境变量后重启 PowerShell 或 VSCode
3. **清理缓存**：删除 `.gradle` 目录并重新编译
4. **检查路径**：确保 JAVA_HOME 路径正确且不包含空格问题

## 🎉 编译成功后

您的多文档预览应用就可以安装到 Android 设备上了：
```powershell
# 安装到连接的设备
.\gradlew.bat installDebug

# 或者手动安装 APK
adb install app/build/outputs/apk/debug/app-debug.apk
```
