import android.content.Context;
import java.util.ArrayList;
import java.util.List;

/**
 * 多文档预览使用示例
 * 
 * 这个类展示了如何使用 DocumentManager 来实现多文档预览功能
 */
public class MultiDocumentExample {
    
    /**
     * 示例1: 基本的多文档预览
     */
    public static void basicMultiDocumentPreview(Context context) {
        // 创建文档管理器
        DocumentManager documentManager = new DocumentManager(context);
        
        // 添加多个文档
        documentManager.addDocument(
            "/storage/emulated/0/Documents/report.pdf",
            "/storage/emulated/0/Documents/temp/report.pdf",
            "年度报告.pdf"
        );
        
        documentManager.addDocument(
            "/storage/emulated/0/Documents/presentation.pptx",
            "/storage/emulated/0/Documents/temp/presentation.pptx",
            "项目演示.pptx"
        );
        
        documentManager.addDocument(
            "/storage/emulated/0/Documents/data.xlsx",
            "/storage/emulated/0/Documents/temp/data.xlsx",
            "数据分析.xlsx"
        );
        
        // 启动多文档预览
        if (documentManager.hasDocuments()) {
            documentManager.startMultiDocumentPreview();
        }
    }
    
    /**
     * 示例2: 批量添加文档
     */
    public static void batchAddDocuments(Context context) {
        DocumentManager documentManager = new DocumentManager(context);
        
        // 创建文档列表
        List<DocumentManager.Document> documents = new ArrayList<>();
        
        // 添加多个文档到列表
        documents.add(new DocumentManager.Document(
            "/path/to/document1.pdf",
            "/path/to/temp/document1.pdf",
            "文档1.pdf"
        ));
        
        documents.add(new DocumentManager.Document(
            "/path/to/document2.docx",
            "/path/to/temp/document2.docx",
            "文档2.docx"
        ));
        
        documents.add(new DocumentManager.Document(
            "/path/to/document3.xlsx",
            "/path/to/temp/document3.xlsx",
            "文档3.xlsx"
        ));
        
        // 批量添加文档
        documentManager.addDocuments(documents);
        
        // 启动预览
        documentManager.startMultiDocumentPreview();
    }
    
    /**
     * 示例3: 动态管理文档
     */
    public static void dynamicDocumentManagement(Context context) {
        DocumentManager documentManager = new DocumentManager(context);
        
        // 添加初始文档
        documentManager.addDocument(
            "/path/to/initial.pdf",
            "/path/to/temp/initial.pdf",
            "初始文档.pdf"
        );
        
        // 检查文档数量
        System.out.println("当前文档数量: " + documentManager.getDocumentCount());
        
        // 添加更多文档
        if (documentManager.getDocumentCount() < 5) {
            documentManager.addDocument(
                "/path/to/additional.docx",
                "/path/to/temp/additional.docx",
                "附加文档.docx"
            );
        }
        
        // 获取所有文档信息
        List<DocumentManager.Document> allDocs = documentManager.getDocuments();
        for (DocumentManager.Document doc : allDocs) {
            System.out.println("文档: " + doc.getFileName() + 
                             " 类型: " + doc.getFileType() + 
                             " 支持: " + doc.isSupportedFormat());
        }
        
        // 启动预览
        documentManager.startMultiDocumentPreview();
    }
    
    /**
     * 示例4: 文件格式检查
     */
    public static void fileFormatValidation(Context context) {
        DocumentManager documentManager = new DocumentManager(context);
        
        // 要检查的文件路径列表
        String[] filePaths = {
            "/path/to/document.pdf",      // 支持
            "/path/to/document.docx",     // 支持
            "/path/to/image.jpg",         // 不支持
            "/path/to/video.mp4",         // 不支持
            "/path/to/spreadsheet.xlsx"   // 支持
        };
        
        // 检查每个文件并只添加支持的格式
        for (String filePath : filePaths) {
            if (DocumentManager.isFileSupported(filePath)) {
                String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
                String tempPath = "/temp/" + fileName;
                
                documentManager.addDocument(filePath, tempPath, fileName);
                System.out.println("已添加支持的文件: " + fileName);
            } else {
                System.out.println("跳过不支持的文件: " + filePath);
            }
        }
        
        // 显示支持的格式列表
        String[] supportedFormats = DocumentManager.getSupportedFormats();
        System.out.println("支持的格式: " + String.join(", ", supportedFormats));
        
        // 启动预览
        if (documentManager.hasDocuments()) {
            documentManager.startMultiDocumentPreview();
        }
    }
    
    /**
     * 示例5: 单文档预览（向后兼容）
     */
    public static void singleDocumentPreview(Context context) {
        // 直接启动单文档预览
        DocumentManager.startSingleDocumentPreview(
            context,
            "/path/to/single-document.pdf",
            "/path/to/temp/single-document.pdf",
            "单个文档.pdf"
        );
    }
    
    /**
     * 示例6: 错误处理和验证
     */
    public static void errorHandlingExample(Context context) {
        DocumentManager documentManager = new DocumentManager(context);
        
        try {
            // 尝试添加文档
            documentManager.addDocument(
                "/path/to/document.pdf",
                "/path/to/temp/document.pdf",
                "测试文档.pdf"
            );
            
            // 检查是否有文档
            if (!documentManager.hasDocuments()) {
                System.out.println("警告: 没有可预览的文档");
                return;
            }
            
            // 检查文档数量
            int count = documentManager.getDocumentCount();
            if (count > 10) {
                System.out.println("警告: 文档数量过多，可能影响性能");
            }
            
            // 启动预览
            documentManager.startMultiDocumentPreview();
            
        } catch (Exception e) {
            System.err.println("启动文档预览时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 示例7: 自定义文档信息
     */
    public static void customDocumentInfo(Context context) {
        DocumentManager documentManager = new DocumentManager(context);
        
        // 创建自定义文档对象
        DocumentManager.Document customDoc = new DocumentManager.Document(
            "/path/to/custom.pdf",
            "/path/to/temp/custom.pdf",
            "自定义文档名称.pdf",
            "pdf"  // 明确指定文件类型
        );
        
        // 检查文档是否支持
        if (customDoc.isSupportedFormat()) {
            documentManager.addDocument(customDoc);
            
            // 输出文档信息
            System.out.println("文档路径: " + customDoc.getFilePath());
            System.out.println("临时路径: " + customDoc.getTempPath());
            System.out.println("文件名: " + customDoc.getFileName());
            System.out.println("文件类型: " + customDoc.getFileType());
            
            documentManager.startMultiDocumentPreview();
        } else {
            System.out.println("不支持的文档格式");
        }
    }
}
