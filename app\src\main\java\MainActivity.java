import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import java.util.ArrayList;
import java.util.List;

/**
 * 主Activity - 演示多文档预览功能
 */
public class MainActivity extends AppCompatActivity {
    private static final int REQUEST_CODE_PICK_FILES = 1001;
    
    private DocumentManager documentManager;
    private TextView selectedFilesText;
    private Button previewButton;
    private LinearLayout selectedFilesLayout;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 创建布局
        createLayout();
        
        // 初始化文档管理器
        documentManager = new DocumentManager(this);
        
        updateUI();
    }
    
    private void createLayout() {
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setPadding(32, 32, 32, 32);
        
        // 标题
        TextView titleText = new TextView(this);
        titleText.setText("多文档预览演示");
        titleText.setTextSize(24);
        titleText.setPadding(0, 0, 0, 32);
        mainLayout.addView(titleText);
        
        // 选择文件按钮
        Button selectFilesButton = new Button(this);
        selectFilesButton.setText("选择文档文件");
        selectFilesButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectFiles();
            }
        });
        mainLayout.addView(selectFilesButton);
        
        // 添加示例文档按钮
        Button addSampleButton = new Button(this);
        addSampleButton.setText("添加示例文档");
        addSampleButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addSampleDocuments();
            }
        });
        mainLayout.addView(addSampleButton);
        
        // 已选择文件显示
        selectedFilesText = new TextView(this);
        selectedFilesText.setText("未选择文件");
        selectedFilesText.setPadding(0, 24, 0, 16);
        mainLayout.addView(selectedFilesText);
        
        // 已选择文件列表
        selectedFilesLayout = new LinearLayout(this);
        selectedFilesLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.addView(selectedFilesLayout);
        
        // 预览按钮
        previewButton = new Button(this);
        previewButton.setText("开始预览");
        previewButton.setEnabled(false);
        previewButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startPreview();
            }
        });
        mainLayout.addView(previewButton);
        
        // 清空按钮
        Button clearButton = new Button(this);
        clearButton.setText("清空文档列表");
        clearButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                clearDocuments();
            }
        });
        mainLayout.addView(clearButton);
        
        setContentView(mainLayout);
    }
    
    private void selectFiles() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.setType("*/*");
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        
        try {
            startActivityForResult(Intent.createChooser(intent, "选择文档"), REQUEST_CODE_PICK_FILES);
        } catch (Exception e) {
            Toast.makeText(this, "无法打开文件选择器", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void addSampleDocuments() {
        // 添加一些示例文档路径（实际使用时需要确保这些文件存在）
        List<DocumentManager.Document> sampleDocs = new ArrayList<>();
        
        // 注意：这些是示例路径，实际使用时需要替换为真实的文件路径
        sampleDocs.add(new DocumentManager.Document(
            "/storage/emulated/0/Documents/sample1.pdf",
            "/storage/emulated/0/Documents/temp/sample1.pdf",
            "示例文档1.pdf"
        ));
        
        sampleDocs.add(new DocumentManager.Document(
            "/storage/emulated/0/Documents/sample2.docx",
            "/storage/emulated/0/Documents/temp/sample2.docx",
            "示例文档2.docx"
        ));
        
        sampleDocs.add(new DocumentManager.Document(
            "/storage/emulated/0/Documents/sample3.xlsx",
            "/storage/emulated/0/Documents/temp/sample3.xlsx",
            "示例表格.xlsx"
        ));
        
        documentManager.addDocuments(sampleDocs);
        updateUI();
        
        Toast.makeText(this, "已添加示例文档（请确保文件路径正确）", Toast.LENGTH_LONG).show();
    }
    
    private void startPreview() {
        if (documentManager.hasDocuments()) {
            documentManager.startMultiDocumentPreview();
        } else {
            Toast.makeText(this, "请先选择文档", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void clearDocuments() {
        documentManager.clearDocuments();
        updateUI();
        Toast.makeText(this, "已清空文档列表", Toast.LENGTH_SHORT).show();
    }
    
    private void updateUI() {
        int count = documentManager.getDocumentCount();
        
        if (count == 0) {
            selectedFilesText.setText("未选择文件");
            previewButton.setEnabled(false);
        } else {
            selectedFilesText.setText("已选择 " + count + " 个文档:");
            previewButton.setEnabled(true);
        }
        
        // 更新文件列表显示
        selectedFilesLayout.removeAllViews();
        List<DocumentManager.Document> docs = documentManager.getDocuments();
        
        for (int i = 0; i < docs.size(); i++) {
            final int index = i;
            DocumentManager.Document doc = docs.get(i);
            
            LinearLayout fileItemLayout = new LinearLayout(this);
            fileItemLayout.setOrientation(LinearLayout.HORIZONTAL);
            fileItemLayout.setPadding(0, 8, 0, 8);
            
            TextView fileNameText = new TextView(this);
            fileNameText.setText((i + 1) + ". " + doc.getFileName());
            fileNameText.setLayoutParams(new LinearLayout.LayoutParams(0, 
                LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f));
            
            Button removeButton = new Button(this);
            removeButton.setText("移除");
            removeButton.setTextSize(12);
            removeButton.setPadding(16, 8, 16, 8);
            removeButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    documentManager.removeDocument(index);
                    updateUI();
                }
            });
            
            fileItemLayout.addView(fileNameText);
            fileItemLayout.addView(removeButton);
            selectedFilesLayout.addView(fileItemLayout);
        }
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == REQUEST_CODE_PICK_FILES && resultCode == Activity.RESULT_OK) {
            if (data != null) {
                if (data.getClipData() != null) {
                    // 多个文件
                    int count = data.getClipData().getItemCount();
                    for (int i = 0; i < count; i++) {
                        Uri uri = data.getClipData().getItemAt(i).getUri();
                        addDocumentFromUri(uri);
                    }
                } else if (data.getData() != null) {
                    // 单个文件
                    Uri uri = data.getData();
                    addDocumentFromUri(uri);
                }
                updateUI();
            }
        }
    }
    
    private void addDocumentFromUri(Uri uri) {
        try {
            String filePath = uri.getPath();
            String fileName = uri.getLastPathSegment();
            
            if (fileName == null) {
                fileName = "未知文档";
            }
            
            // 注意：实际应用中需要处理URI到文件路径的转换
            // 这里简化处理，实际使用时可能需要复制文件到临时目录
            String tempPath = filePath; // 简化处理
            
            if (DocumentManager.isFileSupported(filePath)) {
                documentManager.addDocument(filePath, tempPath, fileName);
            } else {
                Toast.makeText(this, "不支持的文件格式: " + fileName, Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Toast.makeText(this, "添加文件失败", Toast.LENGTH_SHORT).show();
        }
    }
}
