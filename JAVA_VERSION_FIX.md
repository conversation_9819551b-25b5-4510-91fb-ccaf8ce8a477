# Java 版本兼容性问题解决方案

## 🚨 问题描述

编译失败，错误信息：
```
Unsupported class file major version 68
```

这是因为您使用的是 Java 24，但 Android Gradle Plugin 和 Gradle 不支持这个版本。

## 📋 Java 版本对应表

| Java 版本 | Class File Major Version |
|-----------|-------------------------|
| Java 8    | 52                      |
| Java 11   | 55                      |
| Java 17   | 61                      |
| Java 21   | 65                      |
| Java 24   | 68                      |

## 🛠️ 解决方案

### 方案1：安装兼容的 Java 版本（推荐）

#### 1. 安装 Java 11 或 17
```powershell
# 使用 Chocolatey 安装 Java 11
choco install openjdk11

# 或者安装 Java 17
choco install openjdk17
```

#### 2. 设置 JAVA_HOME 环境变量
```powershell
# 设置 Java 11
$env:JAVA_HOME = "C:\Program Files\Eclipse Adoptium\jdk-11.0.x-hotspot"

# 或者设置 Java 17
$env:JAVA_HOME = "C:\Program Files\Eclipse Adoptium\jdk-17.0.x-hotspot"
```

#### 3. 验证 Java 版本
```powershell
java -version
```

### 方案2：使用 Gradle 的 Java Toolchain（高级）

在 `app/build.gradle` 中添加：
```gradle
android {
    // ... 其他配置

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(11)
    }
}
```

### 方案3：使用项目特定的 Java 版本

创建 `gradle.properties` 文件并添加：
```properties
org.gradle.java.home=C:/Program Files/Eclipse Adoptium/jdk-11.0.x-hotspot
```

## 🔧 当前项目配置

我已经将项目配置调整为更兼容的版本：

### build.gradle
- Android Gradle Plugin: 4.2.2
- Gradle Wrapper: 6.7.1

### app/build.gradle
- compileSdkVersion: 30
- targetSdkVersion: 30
- Java 兼容性: 1.8

## 📝 推荐步骤

1. **安装 Java 11**：
   ```powershell
   choco install openjdk11
   ```

2. **设置环境变量**：
   ```powershell
   # 永久设置（需要管理员权限）
   [Environment]::SetEnvironmentVariable("JAVA_HOME", "C:\Program Files\Eclipse Adoptium\jdk-11.0.x-hotspot", "Machine")
   
   # 临时设置（当前会话）
   $env:JAVA_HOME = "C:\Program Files\Eclipse Adoptium\jdk-11.0.x-hotspot"
   ```

3. **重新启动 PowerShell**

4. **验证安装**：
   ```powershell
   java -version
   echo $env:JAVA_HOME
   ```

5. **清理并重新编译**：
   ```powershell
   .\gradlew.bat clean
   .\gradlew.bat assembleDebug
   ```

## 🔍 故障排除

### 如果仍然出现版本问题：

1. **清理 Gradle 缓存**：
   ```powershell
   # 删除 .gradle 目录
   Remove-Item -Recurse -Force $env:USERPROFILE\.gradle\caches
   ```

2. **使用特定 Java 版本运行**：
   ```powershell
   # 临时设置 JAVA_HOME 并运行
   $env:JAVA_HOME = "C:\Program Files\Eclipse Adoptium\jdk-11.0.x-hotspot"
   .\gradlew.bat clean assembleDebug
   ```

3. **检查系统中的 Java 版本**：
   ```powershell
   # 查看所有已安装的 Java 版本
   Get-ChildItem "C:\Program Files\Java"
   Get-ChildItem "C:\Program Files\Eclipse Adoptium"
   ```

## 📚 相关链接

- [OpenJDK 下载](https://adoptium.net/)
- [Android Gradle Plugin 版本兼容性](https://developer.android.com/studio/releases/gradle-plugin)
- [Gradle 版本兼容性](https://docs.gradle.org/current/userguide/compatibility.html)

## ✅ 验证成功

编译成功后，您应该看到类似以下的输出：
```
BUILD SUCCESSFUL in Xs
```

然后可以继续进行应用的构建和测试。
