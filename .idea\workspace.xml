<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="78b23a16-d611-40e1-bf50-2724f61d4b56" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 5
}]]></component>
  <component name="ProjectId" id="301kANICTw6vkd5JZJbFSnqdsJm" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "android.gradle.sync.needed": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/Documents/augment-projects/andorid-doc-preview"
  }
}]]></component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="78b23a16-d611-40e1-bf50-2724f61d4b56" name="Changes" comment="" />
      <created>1752802334209</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752802334209</updated>
    </task>
    <servers />
  </component>
</project>