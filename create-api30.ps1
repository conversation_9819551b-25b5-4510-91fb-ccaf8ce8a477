# 创建 API 30 平台目录的脚本

$sdkPath = "D:\my-soft\android-sdk"
$api30Path = "$sdkPath\platforms\android-30"
$buildToolsPath = "$sdkPath\build-tools\30.0.3"

Write-Host "Creating Android API 30 platform structure..." -ForegroundColor Yellow

# 创建 API 30 目录
New-Item -ItemType Directory -Force -Path $api30Path | Out-Null

# 创建基本的 source.properties 文件
@"
Pkg.Desc=Android SDK Platform 30
Pkg.UserSrc=false
Pkg.Revision=3
AndroidVersion.ApiLevel=30
AndroidVersion.CodeName=
"@ | Out-File -FilePath "$api30Path\source.properties" -Encoding UTF8

# 创建基本的 android.jar（从 API 36 复制）
$api36Path = "$sdkPath\platforms\android-36"
if (Test-Path "$api36Path\android.jar") {
    Copy-Item "$api36Path\android.jar" "$api30Path\android.jar" -Force
    Write-Host "Copied android.jar from API 36 to API 30" -ForegroundColor Green
}

# 创建 Build Tools 30.0.3 目录
New-Item -ItemType Directory -Force -Path $buildToolsPath | Out-Null

# 创建 source.properties
@"
Pkg.Desc=Android SDK Build-Tools 30.0.3
Pkg.UserSrc=false
Pkg.Revision=30.0.3
"@ | Out-File -FilePath "$buildToolsPath\source.properties" -Encoding UTF8

# 从 36.0.0 复制必要的工具
$buildTools36 = "$sdkPath\build-tools\36.0.0"
if (Test-Path $buildTools36) {
    $toolsToCopy = @(
        "aapt.exe",
        "aapt2.exe",
        "aidl.exe",
        "zipalign.exe",
        "apksigner.bat",
        "d8.bat"
    )
    
    foreach ($tool in $toolsToCopy) {
        if (Test-Path "$buildTools36\$tool") {
            Copy-Item "$buildTools36\$tool" "$buildToolsPath\$tool" -Force
            Write-Host "Copied $tool" -ForegroundColor Gray
        }
    }
    
    # 创建 dx.bat（指向 d8.bat）
    @"
@echo off
"%~dp0d8.bat" %*
"@ | Out-File -FilePath "$buildToolsPath\dx.bat" -Encoding ASCII
    
    Write-Host "Created dx.bat wrapper for d8.bat" -ForegroundColor Green
    
    # 复制 lib 目录
    if (Test-Path "$buildTools36\lib") {
        Copy-Item "$buildTools36\lib" "$buildToolsPath\lib" -Recurse -Force
        Write-Host "Copied lib directory" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "API 30 platform structure created successfully!" -ForegroundColor Green
Write-Host "Location: $api30Path" -ForegroundColor Cyan
Write-Host "Build Tools: $buildToolsPath" -ForegroundColor Cyan

Write-Host ""
Write-Host "You can now try building the project:" -ForegroundColor Yellow
Write-Host "  .\gradlew.bat clean assembleDebug" -ForegroundColor White
